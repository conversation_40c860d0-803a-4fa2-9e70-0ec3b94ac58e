# docker run --name bo-cdn -e SOFTGATE_HOST -e MANAGEMENT_API -v $PWD/config/nginx-dev.conf:/etc/nginx/conf.d/default.conf:ro -p 8080:80 nginx nginx-debug -g 'daemon off;'

server {
  listen 80 reuseport default_server;
  server_name  localhost;

  large_client_header_buffers 4 32k;

  location /cdn/ {
    # Wide-open CORS config
    if ($request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      # Custom headers and headers various browsers *should* be OK with but aren't
      add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,InitiatorServiceName';
      # Tell client that this pre-flight info is valid for 20 days
      add_header 'Access-Control-Max-Age' 1728000;
      add_header 'Content-Type' 'text/plain; charset=utf-8';
      add_header 'Content-Length' 0;
      return 204;
    }
    proxy_pass https://storage.googleapis.com/lobby.stg1.m27613.com/;
  }
}
