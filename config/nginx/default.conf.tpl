# Upstream definitions - shared across all virtual hosts
upstream upstream_api {
  server ${MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_integration_api {
  server ${INTEGRATION_API} fail_timeout=30s max_fails=100;
}

upstream upstream_lobby_build_api {
  server ${LOBBY_BUILD_API} fail_timeout=30s max_fails=100;
}

# Default server block - catches unmatched hosts
server {
  listen 80 default_server;
  server_name _;
  return 444; # Close connection without response
}

# Main virtual host - handles standard configuration
server {
  listen 80;
  server_name localhost ~^(?!${SOFTGATE_HOST}${DOLLAR}).*;

  # Include shared configuration
  include /etc/nginx/conf.d/shared.conf;

  location /favicon.ico {
    rewrite ^ /favicon.ico break;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    set $ENV_CONFIG '${ENV_CONFIG} "gosGameProviderCode": "${GOS_GAME_PROVIDER_CODE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "liveChatLicence": "${LIVE_CHAT_LICENCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithBalance": "${CSV_MAX_PLAYERS_WITH_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithoutBalance": "${CSV_MAX_PLAYERS_WITHOUT_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "promotions": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvFileSize": "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvLines": "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${BRIDGE_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${LOGIN_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${CASINO_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${ENGAGEMENT_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${DATA_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${STUDIO_HUB_URL}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  # Include common location blocks
  include /etc/nginx/conf.d/locations.conf;
}

# Softgate virtual host - handles Softgate-specific configuration
server {
  listen 80;
  server_name ${SOFTGATE_HOST};

  # Include shared configuration
  include /etc/nginx/conf.d/shared.conf;

  location /favicon.ico {
    rewrite ^ /img/softgate/favicon.ico break;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    set $ENV_CONFIG '${ENV_CONFIG} "gosGameProviderCode": "${GOS_GAME_PROVIDER_CODE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "liveChatLicence": "${LIVE_CHAT_LICENCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithBalance": "${CSV_MAX_PLAYERS_WITH_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithoutBalance": "${CSV_MAX_PLAYERS_WITHOUT_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "promotions": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvFileSize": "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvLines": "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${SOFTGATE_BRIDGE_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${SOFTGATE_LOGIN_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${SOFTGATE_CASINO_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${SOFTGATE_DATA_HUB_URL}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${SOFTGATE_STUDIO_HUB_URL}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "logo": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "main": "/img/softgate/logo.png",';
    set $ENV_CONFIG '${ENV_CONFIG}   "solo": "/img/softgate/logo.png",';
    set $ENV_CONFIG '${ENV_CONFIG}   "white": ""';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  # Include common location blocks
  include /etc/nginx/conf.d/locations.conf;
}
