#!/usr/bin/env bash
export DOLLAR='$'

if [ -z "${MANAGEMENT_API}" ]; then
  export MANAGEMENT_API='api:3000'
fi

if [ -z "${INTEGRATION_API}" ]; then
  export INTEGRATION_API='integration_tests:3800'
fi

if [ -z "${GOS_GAME_PROVIDER_CODE}" ]; then
  export GOS_GAME_PROVIDER_CODE=PT
fi

if [ -z "${LOBBY_BUILD_API}" ]; then
  export LOBBY_BUILD_API='api-lobby-build:3000'
fi

if [ -z "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}" ]; then
  export PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE=52428800
fi

if [ -z "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}" ]; then
  export PROMO_PARTICIPANTS_MAX_CSV_ITEMS=50000
fi

if [ -z "${GAMES_URL}" ]; then
  export GAMES_URL='https://storage.googleapis.com/lobby.stg1.m27613.com'
fi

if [ -z "${SOFTGATE_HOST}" ]; then
  export SOFTGATE_HOST='unknown'
fi

if [ -z "${SOFTGATE_MANAGEMENT_API}" ]; then
  export SOFTGATE_MANAGEMENT_API="${MANAGEMENT_API}"
fi

echo 'API endpoint: '${MANAGEMENT_API}
echo -e '\nIntegration API endpoint: '${INTEGRATION_API}
echo -e '\nBI GOS Game Provider Code: '${GOS_GAME_PROVIDER_CODE}
echo -e '\nLiveChat licence: '${LIVE_CHAT_LICENCE}
echo -e '\nPromo Participants Max Csv File Size: '${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}
echo -e '\nPromo Participants Max Csv Items: '${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}
echo -e '\nLobby build URL: '${LOBBY_BUILD_API}
echo -e '\nGames URL: '${GAMES_URL}

mkdir -p conf.d

# Process shared configuration
envsubst '$MANAGEMENT_API $SOFTGATE_MANAGEMENT_API $INTEGRATION_API $GOS_GAME_PROVIDER_CODE $LIVE_CHAT_LICENCE $LOBBY_BUILD_API $PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE $PROMO_PARTICIPANTS_MAX_CSV_ITEMS $CSV_MAX_PLAYERS_WITH_BALANCE $CSV_MAX_PLAYERS_WITHOUT_BALANCE $LOBBY_WIDGETS_URL $GAMES_URL $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME' </usr/share/nginx/shared.conf.tpl >/etc/nginx/conf.d/shared.conf

# Process common locations configuration
envsubst '$MANAGEMENT_API $SOFTGATE_MANAGEMENT_API $INTEGRATION_API $GOS_GAME_PROVIDER_CODE $LIVE_CHAT_LICENCE $LOBBY_BUILD_API $PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE $PROMO_PARTICIPANTS_MAX_CSV_ITEMS $CSV_MAX_PLAYERS_WITH_BALANCE $CSV_MAX_PLAYERS_WITHOUT_BALANCE $LOBBY_WIDGETS_URL $GAMES_URL $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME' </usr/share/nginx/locations.conf.tpl >/etc/nginx/conf.d/locations.conf

# Process main virtual hosts configuration
envsubst '$MANAGEMENT_API $SOFTGATE_MANAGEMENT_API $INTEGRATION_API $GOS_GAME_PROVIDER_CODE $LIVE_CHAT_LICENCE $LOBBY_BUILD_API $PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE $PROMO_PARTICIPANTS_MAX_CSV_ITEMS $CSV_MAX_PLAYERS_WITH_BALANCE $CSV_MAX_PLAYERS_WITHOUT_BALANCE $LOBBY_WIDGETS_URL $GAMES_URL $BRIDGE_URL $LOGIN_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME DOLLAR' </usr/share/nginx/default.conf.tpl >/etc/nginx/conf.d/default.conf

nginx -g "daemon off;"
