# Common location blocks for all virtual hosts

location / {
  index /index.html;
  try_files $uri $uri/ /index.html =404;
  access_log off;
}

location /v1 {
  proxy_hide_header 'Access-Control-Allow-Origin';
  proxy_hide_header 'Access-Control-Allow-Methods';
  proxy_hide_header 'Access-Control-Allow-Headers';
  proxy_hide_header 'Access-Control-Allow-Credentials';
  proxy_hide_header 'Access-Control-Max-Age';

  if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Allow-Origin' $http_origin always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE, PATCH, PUT' always;
    add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Max-Age' 2592000 always;
    return 204;
  }

  add_header 'Access-Control-Allow-Origin' $http_origin always;
  add_header 'Access-Control-Allow-Credentials' 'true' always;
  add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;

  proxy_pass http://upstream_api/v1;
}

location /v2 {
  proxy_pass http://upstream_api/v2;
}

location /api/v1/integration {
  proxy_pass http://upstream_integration_api/v1;
}

location /api/v1/srt {
  proxy_pass http://upstream_api/auth-gateway/srt-api/srt;
}

location /auth-gateway {
  proxy_pass http://upstream_api/auth-gateway;
}

location /api/v1/lobby-build/ {
  proxy_pass http://upstream_lobby_build_api/;
}

# Game provider
location /gameprovider/v1 {
  proxy_pass http://upstream_api/gameprovider/v1;
}

location /gameprovider/v2 {
  proxy_pass http://upstream_api/gameprovider/v2;
}

# Players
location /player/v1 {
  proxy_pass http://upstream_api/player/v1;
}

# site
location /site/v1 {
  proxy_pass http://upstream_api/site/v1;
}

location /cdn/ {
  proxy_pass ${GAMES_URL}/;
}

location /widgets {
  proxy_pass ${LOBBY_WIDGETS_URL}/widgets;
}
