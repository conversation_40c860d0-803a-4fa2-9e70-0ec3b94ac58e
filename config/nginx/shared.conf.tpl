# Shared configuration settings for all virtual hosts

# Common server settings
userid_name 'uid';
userid_path '/; HttpOnly';
userid_expires 365d;
userid         on;
server_tokens off;

# Gzip compression settings
gzip on;
gzip_comp_level 3;
gzip_static on;
gzip_min_length 2048;
gzip_buffers      16 8k;
gzip_vary         on;
gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml
image/svg+xml application/xml+rss image/x-icon image/bmp;
gzip_disable "msie6";

# Document root
root /usr/share/nginx/html/;

# Proxy buffer settings
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;

# Client settings
large_client_header_buffers 4 32k;
client_max_body_size 32m;

# Timeout settings
proxy_connect_timeout 300;
proxy_send_timeout 300;
proxy_read_timeout 300;
send_timeout 300;

# Cache settings
expires -1;
etag off;
