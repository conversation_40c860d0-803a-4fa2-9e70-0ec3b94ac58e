#!/bin/bash

# Quick nginx configuration test
set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

CONTAINER_NAME="nginx-quick-test"
PORT="8081"

echo -e "${BLUE}=== Quick Nginx Configuration Test ===${NC}"

# Cleanup function
cleanup() {
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
}
trap cleanup EXIT

# Create minimal test setup
cat > Dockerfile.quick << 'EOF'
FROM nginx:alpine
COPY config/nginx/*.tpl /usr/share/nginx/
COPY config/nginx/run.sh /usr/local/bin/run.sh
RUN chmod +x /usr/local/bin/run.sh
RUN echo '<h1>Test Page</h1>' > /usr/share/nginx/html/index.html
RUN mkdir -p /usr/share/nginx/html/img/softgate
RUN echo 'softgate-favicon' > /usr/share/nginx/html/img/softgate/favicon.ico
EXPOSE 80
CMD ["/usr/local/bin/run.sh"]
EOF

echo "Building and starting nginx..."
docker build -f Dockerfile.quick -t nginx-quick-test . > /dev/null 2>&1

docker run -d --name $CONTAINER_NAME -p $PORT:80 \
    -e MANAGEMENT_API="api:3000" \
    -e INTEGRATION_API="integration:3800" \
    -e GOS_GAME_PROVIDER_CODE="PT" \
    -e LOBBY_BUILD_API="lobby:3000" \
    -e PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE="52428800" \
    -e PROMO_PARTICIPANTS_MAX_CSV_ITEMS="50000" \
    -e GAMES_URL="https://games.test.com" \
    -e SOFTGATE_HOST="softgate.test.com" \
    -e SOFTGATE_MANAGEMENT_API="api:3000" \
    -e LIVE_CHAT_LICENCE="test" \
    -e CSV_MAX_PLAYERS_WITH_BALANCE="1000" \
    -e CSV_MAX_PLAYERS_WITHOUT_BALANCE="5000" \
    -e LOBBY_WIDGETS_URL="https://widgets.test.com" \
    -e BRIDGE_URL="https://bridge.test.com" \
    -e LOGIN_URL="https://login.test.com" \
    -e CASINO_HUB_URL="https://casino.test.com" \
    -e ENGAGEMENT_HUB_URL="https://engagement.test.com" \
    -e DATA_HUB_URL="https://data.test.com" \
    -e STUDIO_HUB_URL="https://studio.test.com" \
    -e SOFTGATE_BRIDGE_URL="https://softgate-bridge.test.com" \
    -e SOFTGATE_LOGIN_URL="https://softgate-login.test.com" \
    -e SOFTGATE_CASINO_HUB_URL="https://softgate-casino.test.com" \
    -e SOFTGATE_ENGAGEMENT_HUB_URL="https://softgate-engagement.test.com" \
    -e SOFTGATE_DATA_HUB_URL="https://softgate-data.test.com" \
    -e SOFTGATE_STUDIO_HUB_URL="https://softgate-studio.test.com" \
    -e ENV_NAME="test" \
    -e LOCATION_NAME="test-location" \
    nginx-quick-test > /dev/null 2>&1

sleep 3

# Test nginx is running
if curl -s http://localhost:$PORT > /dev/null; then
    echo -e "${GREEN}✓ Nginx is running${NC}"
else
    echo -e "${RED}✗ Nginx failed to start${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test virtual hosts
echo "Testing virtual hosts..."

# Test main host
if curl -s -H "Host: localhost" http://localhost:$PORT/api/config | grep -q "bridge.*bridge.test.com"; then
    echo -e "${GREEN}✓ Main virtual host working${NC}"
else
    echo -e "${RED}✗ Main virtual host failed${NC}"
fi

# Test Softgate host
if curl -s -H "Host: softgate.test.com" http://localhost:$PORT/api/config | grep -q "bridge.*softgate-bridge.test.com"; then
    echo -e "${GREEN}✓ Softgate virtual host working${NC}"
else
    echo -e "${RED}✗ Softgate virtual host failed${NC}"
fi

# Test default server (unknown host)
status=$(curl -s -w "%{http_code}" -H "Host: unknown.host" http://localhost:$PORT/ -o /dev/null)
if [ "$status" = "444" ]; then
    echo -e "${GREEN}✓ Default server block working (returns 444)${NC}"
else
    echo -e "${RED}✗ Default server block failed (got $status instead of 444)${NC}"
fi

echo -e "\n${BLUE}Configuration files generated:${NC}"
echo "Main config:"
docker exec $CONTAINER_NAME head -20 /etc/nginx/conf.d/default.conf

echo -e "\n${GREEN}Test completed successfully!${NC}"
echo "Nginx is running on http://localhost:$PORT"
echo "Test with: curl -H 'Host: localhost' http://localhost:$PORT/api/config"
echo "Or: curl -H 'Host: softgate.test.com' http://localhost:$PORT/api/config"

# Clean up temp files
rm -f Dockerfile.quick

echo "Press Ctrl+C to stop the container"
wait
