#!/bin/bash

# Test script for nginx virtual host configuration
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="nginx-vhost-test"
IMAGE_NAME="nginx-vhost-test"
NGINX_PORT="8080"
SOFTGATE_HOST="softgate.test.com"

# Test environment variables
export MANAGEMENT_API="api:3000"
export INTEGRATION_API="integration_tests:3800"
export GOS_GAME_PROVIDER_CODE="PT"
export LOBBY_BUILD_API="api-lobby-build:3000"
export PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE="52428800"
export PROMO_PARTICIPANTS_MAX_CSV_ITEMS="50000"
export GAMES_URL="https://storage.googleapis.com/lobby.stg1.m27613.com"
export SOFTGATE_HOST="$SOFTGATE_HOST"
export SOFTGATE_MANAGEMENT_API="$MANAGEMENT_API"
export LIVE_CHAT_LICENCE="test-licence"
export CSV_MAX_PLAYERS_WITH_BALANCE="1000"
export CSV_MAX_PLAYERS_WITHOUT_BALANCE="5000"
export LOBBY_WIDGETS_URL="https://widgets.test.com"
export BRIDGE_URL="https://bridge.test.com"
export LOGIN_URL="https://login.test.com"
export CASINO_HUB_URL="https://casino.test.com"
export ENGAGEMENT_HUB_URL="https://engagement.test.com"
export DATA_HUB_URL="https://data.test.com"
export STUDIO_HUB_URL="https://studio.test.com"
export SOFTGATE_BRIDGE_URL="https://softgate-bridge.test.com"
export SOFTGATE_LOGIN_URL="https://softgate-login.test.com"
export SOFTGATE_CASINO_HUB_URL="https://softgate-casino.test.com"
export SOFTGATE_ENGAGEMENT_HUB_URL="https://softgate-engagement.test.com"
export SOFTGATE_DATA_HUB_URL="https://softgate-data.test.com"
export SOFTGATE_STUDIO_HUB_URL="https://softgate-studio.test.com"
export ENV_NAME="test"
export LOCATION_NAME="test-location"

echo -e "${BLUE}=== Nginx Virtual Host Configuration Test ===${NC}"

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    docker rmi $IMAGE_NAME 2>/dev/null || true
}

# Trap cleanup on exit
trap cleanup EXIT

# Create Dockerfile for testing
print_status "Creating test Dockerfile..."
cat > Dockerfile.test << 'EOF'
FROM nginx:alpine

# Copy configuration templates
COPY config/nginx/*.tpl /usr/share/nginx/
COPY config/nginx/run.sh /usr/local/bin/run.sh

# Create a simple index.html for testing
RUN echo '<html><body><h1>Nginx Virtual Host Test</h1><p>Host: {{HOST}}</p></body></html>' > /usr/share/nginx/html/index.html

# Create softgate favicon directory and file
RUN mkdir -p /usr/share/nginx/html/img/softgate
RUN echo 'softgate-favicon' > /usr/share/nginx/html/img/softgate/favicon.ico

# Make run script executable
RUN chmod +x /usr/local/bin/run.sh

EXPOSE 80

CMD ["/usr/local/bin/run.sh"]
EOF

# Build Docker image
print_status "Building Docker image..."
if docker build -f Dockerfile.test -t $IMAGE_NAME .; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Run container
print_status "Starting nginx container on port $NGINX_PORT..."
if docker run -d \
    --name $CONTAINER_NAME \
    -p $NGINX_PORT:80 \
    -e MANAGEMENT_API="$MANAGEMENT_API" \
    -e INTEGRATION_API="$INTEGRATION_API" \
    -e GOS_GAME_PROVIDER_CODE="$GOS_GAME_PROVIDER_CODE" \
    -e LOBBY_BUILD_API="$LOBBY_BUILD_API" \
    -e PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE="$PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE" \
    -e PROMO_PARTICIPANTS_MAX_CSV_ITEMS="$PROMO_PARTICIPANTS_MAX_CSV_ITEMS" \
    -e GAMES_URL="$GAMES_URL" \
    -e SOFTGATE_HOST="$SOFTGATE_HOST" \
    -e SOFTGATE_MANAGEMENT_API="$SOFTGATE_MANAGEMENT_API" \
    -e LIVE_CHAT_LICENCE="$LIVE_CHAT_LICENCE" \
    -e CSV_MAX_PLAYERS_WITH_BALANCE="$CSV_MAX_PLAYERS_WITH_BALANCE" \
    -e CSV_MAX_PLAYERS_WITHOUT_BALANCE="$CSV_MAX_PLAYERS_WITHOUT_BALANCE" \
    -e LOBBY_WIDGETS_URL="$LOBBY_WIDGETS_URL" \
    -e BRIDGE_URL="$BRIDGE_URL" \
    -e LOGIN_URL="$LOGIN_URL" \
    -e CASINO_HUB_URL="$CASINO_HUB_URL" \
    -e ENGAGEMENT_HUB_URL="$ENGAGEMENT_HUB_URL" \
    -e DATA_HUB_URL="$DATA_HUB_URL" \
    -e STUDIO_HUB_URL="$STUDIO_HUB_URL" \
    -e SOFTGATE_BRIDGE_URL="$SOFTGATE_BRIDGE_URL" \
    -e SOFTGATE_LOGIN_URL="$SOFTGATE_LOGIN_URL" \
    -e SOFTGATE_CASINO_HUB_URL="$SOFTGATE_CASINO_HUB_URL" \
    -e SOFTGATE_ENGAGEMENT_HUB_URL="$SOFTGATE_ENGAGEMENT_HUB_URL" \
    -e SOFTGATE_DATA_HUB_URL="$SOFTGATE_DATA_HUB_URL" \
    -e SOFTGATE_STUDIO_HUB_URL="$SOFTGATE_STUDIO_HUB_URL" \
    -e ENV_NAME="$ENV_NAME" \
    -e LOCATION_NAME="$LOCATION_NAME" \
    $IMAGE_NAME; then
    print_success "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for nginx to start
print_status "Waiting for nginx to start..."
sleep 5

# Check if container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container is not running"
    docker logs $CONTAINER_NAME
    exit 1
fi

print_success "Container is running"

# Test functions
test_endpoint() {
    local host="$1"
    local path="$2"
    local expected_status="$3"
    local description="$4"
    
    print_status "Testing: $description"
    
    local response
    local status_code
    
    if [ -n "$host" ]; then
        response=$(curl -s -w "\n%{http_code}" -H "Host: $host" "http://localhost:$NGINX_PORT$path" || echo -e "\n000")
    else
        response=$(curl -s -w "\n%{http_code}" "http://localhost:$NGINX_PORT$path" || echo -e "\n000")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "✓ $description (Status: $status_code)"
        return 0
    else
        print_error "✗ $description (Expected: $expected_status, Got: $status_code)"
        if [ "$status_code" != "000" ]; then
            echo "Response body: $body"
        fi
        return 1
    fi
}

# Run tests
echo -e "\n${BLUE}=== Running Tests ===${NC}"

# Test default server (should return 444 for unknown hosts)
test_endpoint "unknown.host.com" "/" "444" "Default server block (unknown host)"

# Test main virtual host
test_endpoint "localhost" "/" "200" "Main virtual host - root path"
test_endpoint "localhost" "/favicon.ico" "200" "Main virtual host - favicon"
test_endpoint "localhost" "/api/config" "200" "Main virtual host - API config"

# Test Softgate virtual host
test_endpoint "$SOFTGATE_HOST" "/" "200" "Softgate virtual host - root path"
test_endpoint "$SOFTGATE_HOST" "/favicon.ico" "200" "Softgate virtual host - favicon (custom)"
test_endpoint "$SOFTGATE_HOST" "/api/config" "200" "Softgate virtual host - API config"

# Test API config content
print_status "Testing API config content..."

# Main host config
main_config=$(curl -s -H "Host: localhost" "http://localhost:$NGINX_PORT/api/config")
if echo "$main_config" | grep -q "\"bridge\": \"$BRIDGE_URL\""; then
    print_success "✓ Main host uses standard bridge URL"
else
    print_error "✗ Main host bridge URL incorrect"
fi

# Softgate host config
softgate_config=$(curl -s -H "Host: $SOFTGATE_HOST" "http://localhost:$NGINX_PORT/api/config")
if echo "$softgate_config" | grep -q "\"bridge\": \"$SOFTGATE_BRIDGE_URL\""; then
    print_success "✓ Softgate host uses Softgate bridge URL"
else
    print_error "✗ Softgate host bridge URL incorrect"
fi

if echo "$softgate_config" | grep -q "\"main\": \"/img/softgate/logo.png\""; then
    print_success "✓ Softgate host uses custom logo"
else
    print_error "✗ Softgate host logo configuration incorrect"
fi

# Show nginx configuration
echo -e "\n${BLUE}=== Generated Nginx Configuration ===${NC}"
docker exec $CONTAINER_NAME cat /etc/nginx/conf.d/default.conf

echo -e "\n${BLUE}=== Container Logs ===${NC}"
docker logs $CONTAINER_NAME

echo -e "\n${GREEN}=== Test Complete ===${NC}"
print_status "You can manually test the endpoints at:"
print_status "  Main host: http://localhost:$NGINX_PORT (Host: localhost)"
print_status "  Softgate host: http://localhost:$NGINX_PORT (Host: $SOFTGATE_HOST)"
print_status ""
print_status "Use curl with Host header:"
print_status "  curl -H 'Host: localhost' http://localhost:$NGINX_PORT/api/config"
print_status "  curl -H 'Host: $SOFTGATE_HOST' http://localhost:$NGINX_PORT/api/config"

# Keep container running for manual testing
read -p "Press Enter to stop the container and cleanup..."
